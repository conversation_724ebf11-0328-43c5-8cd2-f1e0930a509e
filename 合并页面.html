<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fede Vigevani YouTube网红频道详情与完整数据分析报告 - 合并页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f7f8fa;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #101c32;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .header p {
            color: #7d849b;
            font-size: 14px;
        }

        .tab-container {
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .tab-navigation {
            display: flex;
            background: #f7f8fa;
            border-bottom: 1px solid #e5e7eb;
            padding: 0;
        }

        .tab-button {
            flex: 1;
            padding: 16px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #7d849b;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab-button:hover {
            background: #fff;
            color: #fa6300;
        }

        .tab-button.active {
            background: #fff;
            color: #fa6300;
            border-bottom: 3px solid #fa6300;
        }

        .tab-button::before {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 8px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .tab-button[data-tab="overview"]::before {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>');
        }

        .tab-button[data-tab="audience"]::before {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H17c-.8 0-1.54.37-2.01.99L14 10l-1.99-1.01A2.5 2.5 0 0 0 10 8H8.46c-.8 0-1.49.59-1.42 1.37L9.5 16H12v6h8zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5z"/></svg>');
        }

        .tab-button[data-tab="content"]::before {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>');
        }

        .tab-button[data-tab="brand"]::before {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>');
        }

        .tab-content {
            position: relative;
            height: calc(100vh - 200px);
            min-height: 600px;
        }

        .tab-pane {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .tab-pane.active {
            opacity: 1;
            visibility: visible;
        }

        .tab-pane iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 0 0 20px 20px;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #7d849b;
            font-size: 16px;
        }

        .loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #fa6300;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .tab-button {
                padding: 12px 16px;
                font-size: 14px;
            }

            .tab-button::before {
                width: 16px;
                height: 16px;
            }

            .tab-content {
                height: calc(100vh - 180px);
                min-height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Fede Vigevani YouTube网红频道详情与完整数据分析报告</h1>
            <p>实时追踪和了解YouTube观看量、频道收入、网红报价以及每日最热YouTube视频</p>
        </div>

        <div class="tab-container">
            <div class="tab-navigation">
                <button class="tab-button active" data-tab="overview">
                    数据总览
                </button>
                <button class="tab-button" data-tab="audience">
                    受众数据
                </button>
                <button class="tab-button" data-tab="content">
                    内容数据
                </button>
                <button class="tab-button" data-tab="brand">
                    品牌数据
                </button>
            </div>

            <div class="tab-content">
                <div class="tab-pane active" id="overview">
                    <div class="loading">正在加载数据总览...</div>
                    <iframe src="data_数据总览.html" onload="hideLoading(this)"></iframe>
                </div>
                <div class="tab-pane" id="audience">
                    <div class="loading">正在加载受众数据...</div>
                    <iframe src="data_受众数据.html" onload="hideLoading(this)"></iframe>
                </div>
                <div class="tab-pane" id="content">
                    <div class="loading">正在加载内容数据...</div>
                    <iframe src="data_内容数据.html" onload="hideLoading(this)"></iframe>
                </div>
                <div class="tab-pane" id="brand">
                    <div class="loading">正在加载品牌数据...</div>
                    <iframe src="data_品牌数据.html" onload="hideLoading(this)"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换功能
        function switchTab(tabId) {
            // 移除所有活跃状态
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });

            // 激活选中的页签
            document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');

            // 保存当前页签状态
            localStorage.setItem('activeTab', tabId);
        }

        // 隐藏加载提示
        function hideLoading(iframe) {
            const loadingDiv = iframe.previousElementSibling;
            if (loadingDiv && loadingDiv.classList.contains('loading')) {
                loadingDiv.style.display = 'none';
            }
        }

        // 初始化页签事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 恢复上次选中的页签
            const savedTab = localStorage.getItem('activeTab');
            if (savedTab) {
                switchTab(savedTab);
            }

            // 绑定页签点击事件
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId);
                });
            });

            // 键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                if (e.altKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            switchTab('overview');
                            break;
                        case '2':
                            e.preventDefault();
                            switchTab('audience');
                            break;
                        case '3':
                            e.preventDefault();
                            switchTab('content');
                            break;
                        case '4':
                            e.preventDefault();
                            switchTab('brand');
                            break;
                    }
                }
            });
        });

        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('页面加载错误:', e);
        });
    </script>
</body>
</html>
